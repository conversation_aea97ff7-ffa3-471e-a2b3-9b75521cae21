#!/bin/sh /etc/rc.common

START=99
USE_PROCD=1
PROG=/usr/bin/python3
SCRIPT_PATH="/home/<USER>/bms_Uploader.py"
CONFIG_PATH="/home/<USER>/config.py"

start_service() {
    # 检查必要文件是否存在
    [ -e "$SCRIPT_PATH" ] && [ -e "$CONFIG_PATH" ] && {
        procd_open_instance
        procd_set_param command "$PROG" "$SCRIPT_PATH"
        procd_set_param env PYTHONPATH=/data/pythonpackages:/home/<USER>
        procd_set_param stdout 1
        procd_set_param stderr 1
        procd_set_param respawn 3600 10 0
        procd_close_instance
    }
}

