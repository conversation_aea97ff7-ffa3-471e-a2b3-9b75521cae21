#!/bin/sh


# 需要先 chmod +x can_bms_uploader.sh
SCRIPT_DIR="/home/<USER>"
PYTHON="/usr/bin/python3"
SCRIPT="$SCRIPT_DIR/bms_Uploader.py"

LOG_FILE="$SCRIPT_DIR/output.log" #程序打印
ERR_FILE="$SCRIPT_DIR/uploader_error.log" #程序内部错误日志
PID_FILE="$SCRIPT_DIR/bms_Uploader.pid"

JSON_FILE="$SCRIPT_DIR/can_data_backup.json"

echo "[+] 初始化运行环境..."

# === 创建目录和文件 ===
touch "$LOG_FILE" "$ERR_FILE" "$PID_FILE" "$JSON_FILE"

# === 安装依赖 ===
# echo "[+] 安装Python依赖..."
# pip3 install python-can requests

# === 设置权限 ===
chmod -R 755 "$SCRIPT_DIR"
chmod 664 "$LOG_FILE" "$ERR_FILE" "$PID_FILE" "$JSON_FILE"

# === 清理残留进程 ===
echo "[+] 停止现有进程..."
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if [ -n "$PID" ] && ps -p "$PID" > /dev/null 2>&1; then
        echo "[i] 终止残留进程 PID: $PID"
        kill "$PID" && sleep 1
        ps -p "$PID" > /dev/null 2>&1 && kill -9 "$PID"
    fi
    rm -f "$PID_FILE"
fi

# === 注册定时日志清理任务（每6小时） ===
echo "[+] 检查 crontab 清理任务..."
crontab -l 2>/dev/null | grep -q "clean_log.sh"
if [ $? -ne 0 ]; then
    (crontab -l 2>/dev/null; echo "0 */6 * * * $SCRIPT_DIR/clean_log.sh") | crontab -
    echo "[✓] 已添加 crontab 任务"
else
    echo "[i] 已存在清理任务，跳过"
fi

# === 启动 Python 脚本，并输出日志 ===
echo "[+] 启动 Python 脚本..."
$PYTHON "$SCRIPT" >> "$LOG_FILE" 2>&1 &

PID=$!
echo $PID > "$PID_FILE"

echo "[✓] 启动完成 (PID: $PID)"