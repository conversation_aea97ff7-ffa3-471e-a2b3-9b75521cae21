#!/bin/sh

SCRIPT_DIR="/home/<USER>"
LOG_FILE="$SCRIPT_DIR/output.log"
ERR_FILE="$SCRIPT_DIR/uploader_error.log"
PID_FILE="$SCRIPT_DIR/bms_Uploader.pid"
JSON_FILE="$SCRIPT_DIR/can_data_backup.json"

echo "[+] 初始化运行环境..."

# 创建必要文件
touch "$LOG_FILE" "$ERR_FILE" "$PID_FILE" "$JSON_FILE"

chmod -R 755 "$SCRIPT_DIR"
chmod 664 "$LOG_FILE" "$ERR_FILE" "$PID_FILE" "$JSON_FILE"

# 清理旧进程
echo "[+] 停止现有进程..."
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if [ -n "$PID" ] && ps -p "$PID" > /dev/null 2>&1; then
        echo "[i] 终止残留进程 PID: $PID"
        kill "$PID" && sleep 1
        ps -p "$PID" > /dev/null 2>&1 && kill -9 "$PID"
    fi
    rm -f "$PID_FILE"
fi

# 添加清理任务
echo "[+] 检查 crontab 清理任务..."
crontab -l 2>/dev/null | grep -q "clean_log.sh"
if [ $? -ne 0 ]; then
    (crontab -l 2>/dev/null; echo "0 */6 * * * $SCRIPT_DIR/clean_log.sh") | crontab -
    echo "[✓] 已添加 crontab 任务"
else
    echo "[i] 已存在清理任务，跳过"
fi