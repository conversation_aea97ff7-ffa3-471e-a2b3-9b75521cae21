#!/bin/sh

# BMS CAN Logger 部署脚本
# 集成 bms_uploader + bms_can_service + clean_log

SCRIPT_DIR="/home/<USER>"
SERVICE_FILE="/etc/init.d/bms_can_service"

echo "[+] BMS CAN Logger 部署开始..."

# === 创建目录和文件 ===
echo "[+] 创建目录和文件..."
mkdir -p "$SCRIPT_DIR"
touch "$SCRIPT_DIR/output.log" "$SCRIPT_DIR/uploader_error.log" "$SCRIPT_DIR/bms_Uploader.pid" "$SCRIPT_DIR/can_data_backup.json"
chmod -R 755 "$SCRIPT_DIR"
chmod 664 "$SCRIPT_DIR"/*.log "$SCRIPT_DIR"/*.json "$SCRIPT_DIR"/*.pid 2>/dev/null || true

# === 安装依赖 ===
# echo "[+] 安装依赖..."
# pip3 install python-can requests 2>/dev/null || opkg install python3-can python3-requests

# === 停止现有服务 ===
echo "[+] 停止现有服务..."
[ -f "$SERVICE_FILE" ] && "$SERVICE_FILE" stop 2>/dev/null
pkill -f "bms_Uploader.py" 2>/dev/null || true

# === 创建服务文件 ===
echo "[+] 创建服务文件..."
cat > "$SERVICE_FILE" << 'EOF'
#!/bin/sh /etc/rc.common

START=99
USE_PROCD=1
PROG=/usr/bin/python3
SCRIPT_PATH="/home/<USER>/bms_Uploader.py"
CONFIG_PATH="/home/<USER>/config.py"

start_service() {
    [ -e "$SCRIPT_PATH" ] && [ -e "$CONFIG_PATH" ] && {
        procd_open_instance
        procd_set_param command "$PROG" "$SCRIPT_PATH"
        procd_set_param env PYTHONPATH=/data/pythonpackages:/home/<USER>
        procd_set_param stdout 1
        procd_set_param stderr 1
        procd_set_param respawn 3600 10 0
        procd_close_instance
    }
}
EOF
chmod +x "$SERVICE_FILE"

# === 创建清理脚本 ===
echo "[+] 创建清理脚本..."
cat > "$SCRIPT_DIR/clean_log.sh" << 'EOF'
#!/bin/sh
LOG_DIR="/home/<USER>"
MAX_SIZE=1048576  # 1MB

for file in can_data_backup.json uploader_error.log output.log; do
    FILE_PATH="$LOG_DIR/$file"
    if [ -f "$FILE_PATH" ]; then
        size=$(wc -c < "$FILE_PATH" 2>/dev/null)
        if [ $size -gt $MAX_SIZE ]; then
            [ "$file" = "can_data_backup.json" ] && echo "{}" > "$FILE_PATH" || echo "" > "$FILE_PATH"
            echo "[$(date)] 清空日志: $FILE_PATH (${size} 字节)"
        fi
    fi
done
EOF
chmod +x "$SCRIPT_DIR/clean_log.sh"

# === 添加定时任务 ===
echo "[+] 添加定时清理任务..."
crontab -l 2>/dev/null | grep -q "clean_log.sh" || {
    (crontab -l 2>/dev/null; echo "0 */6 * * * $SCRIPT_DIR/clean_log.sh") | crontab -
    echo "[✓] 已添加定时任务"
}

# === 启动服务 ===
echo "[+] 启动服务..."
"$SERVICE_FILE" enable
"$SERVICE_FILE" start
sleep 2

# === 检查状态 ===
echo ""
echo "=== 部署完成 ==="
echo "服务文件: $SERVICE_FILE"
echo "主程序: $SCRIPT_DIR/bms_Uploader.py"
echo "配置文件: $SCRIPT_DIR/config.py"
echo ""
echo "常用命令:"
echo "  查看日志: tail -f $SCRIPT_DIR/output.log"
echo "  重启服务: $SERVICE_FILE restart"
echo "  查看状态: $SERVICE_FILE status"
echo "  手动清理: $SCRIPT_DIR/clean_log.sh"
echo ""
ps | grep "bms_Uploader.py" | grep -v grep && echo "[✓] 服务运行正常" || echo "[!] 服务可能未启动"
