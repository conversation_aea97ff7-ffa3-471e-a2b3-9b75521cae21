#!/bin/sh

# BMS CAN Logger 一键部署脚本
# 集成 bms_uploader, bms_can_service, clean_log 功能
# 适用于 OpenWrt 系统

# === 配置参数 ===
SCRIPT_DIR="/home/<USER>"
PYTHON="/usr/bin/python3"
MAIN_SCRIPT="$SCRIPT_DIR/bms_Uploader.py"
CONFIG_FILE="$SCRIPT_DIR/config.py"
CLEAN_SCRIPT="$SCRIPT_DIR/clean_log.sh"
SERVICE_NAME="bms_can_service"
SERVICE_FILE="/etc/init.d/$SERVICE_NAME"

# 日志文件
OUTPUT_LOG="$SCRIPT_DIR/output.log"
ERROR_LOG="$SCRIPT_DIR/uploader_error.log"
PID_FILE="$SCRIPT_DIR/bms_Uploader.pid"
DATA_BACKUP="$SCRIPT_DIR/can_data_backup.json"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# === 检查系统环境 ===
check_environment() {
    print_step "检查系统环境..."
    
    # 检查 Python3
    if ! command -v python3 >/dev/null 2>&1; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    # 检查必要文件
    if [ ! -f "$MAIN_SCRIPT" ]; then
        print_error "主程序文件不存在: $MAIN_SCRIPT"
        exit 1
    fi
    
    if [ ! -f "$CONFIG_FILE" ]; then
        print_error "配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    print_info "环境检查通过"
}

# === 创建目录和文件 ===
setup_directories() {
    print_step "创建目录和文件..."
    
    # 创建主目录
    mkdir -p "$SCRIPT_DIR"
    
    # 创建必要文件
    touch "$OUTPUT_LOG" "$ERROR_LOG" "$PID_FILE" "$DATA_BACKUP"
    
    # 设置权限
    chmod -R 755 "$SCRIPT_DIR"
    chmod 664 "$OUTPUT_LOG" "$ERROR_LOG" "$PID_FILE" "$DATA_BACKUP"
    
    print_info "目录和文件创建完成"
}

# === 安装 Python 依赖 ===
install_dependencies() {
    print_step "安装 Python 依赖..."
    
    # 检查并安装依赖
    pip3 install python-can requests 2>/dev/null || {
        print_warn "pip3 安装依赖失败，尝试使用 opkg..."
        opkg update
        opkg install python3-can python3-requests
    }
    
    print_info "依赖安装完成"
}

# === 停止现有服务 ===
stop_existing_service() {
    print_step "停止现有服务..."
    
    # 停止 procd 服务
    if [ -f "$SERVICE_FILE" ]; then
        "$SERVICE_FILE" stop 2>/dev/null || true
        "$SERVICE_FILE" disable 2>/dev/null || true
    fi
    
    # 清理残留进程
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if [ -n "$PID" ] && ps -p "$PID" > /dev/null 2>&1; then
            print_info "终止残留进程 PID: $PID"
            kill "$PID" && sleep 1
            ps -p "$PID" > /dev/null 2>&1 && kill -9 "$PID"
        fi
        rm -f "$PID_FILE"
    fi
    
    # 清理其他可能的 Python 进程
    pkill -f "bms_Uploader.py" 2>/dev/null || true
    
    print_info "现有服务已停止"
}

# === 创建服务文件 ===
create_service_file() {
    print_step "创建 procd 服务文件..."
    
    cat > "$SERVICE_FILE" << 'EOF'
#!/bin/sh /etc/rc.common

START=99
USE_PROCD=1
PROG=/usr/bin/python3
SCRIPT_PATH="/home/<USER>/bms_Uploader.py"
CONFIG_PATH="/home/<USER>/config.py"

start_service() {
    # 检查必要文件是否存在
    [ -e "$SCRIPT_PATH" ] && [ -e "$CONFIG_PATH" ] && {
        procd_open_instance
        procd_set_param command "$PROG" "$SCRIPT_PATH"
        procd_set_param env PYTHONPATH=/data/pythonpackages:/home/<USER>
        procd_set_param stdout 1
        procd_set_param stderr 1
        procd_set_param respawn 3600 10 0
        procd_close_instance
    }
}
EOF
    
    chmod +x "$SERVICE_FILE"
    print_info "服务文件创建完成"
}

# === 设置日志清理任务 ===
setup_log_cleanup() {
    print_step "设置日志清理任务..."
    
    # 确保清理脚本存在且可执行
    if [ -f "$CLEAN_SCRIPT" ]; then
        chmod +x "$CLEAN_SCRIPT"
    else
        print_warn "清理脚本不存在，创建默认清理脚本..."
        create_default_clean_script
    fi
    
    # 检查并添加 crontab 任务
    crontab -l 2>/dev/null | grep -q "clean_log.sh"
    if [ $? -ne 0 ]; then
        (crontab -l 2>/dev/null; echo "0 */6 * * * $CLEAN_SCRIPT") | crontab -
        print_info "已添加日志清理定时任务 (每6小时执行一次)"
    else
        print_info "日志清理任务已存在，跳过"
    fi
}

# === 创建默认清理脚本 ===
create_default_clean_script() {
    cat > "$CLEAN_SCRIPT" << 'EOF'
#!/bin/sh

# CAN Logger 日志清理脚本
LOG_DIR="/home/<USER>"
LOG_FILES="can_data_backup.json uploader_error.log output.log"
MAX_SIZE=1048576  # 1MB

echo "[$(date '+%Y-%m-%d %H:%M:%S')] 开始日志清理检查..."

# 清理日志文件
for file in $LOG_FILES; do
    FILE_PATH="$LOG_DIR/$file"
    if [ -f "$FILE_PATH" ]; then
        size=$(wc -c < "$FILE_PATH" 2>/dev/null)
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] ${file} 文件大小: ${size} 字节"
        if [ $size -gt $MAX_SIZE ]; then
            if [ "$file" = "can_data_backup.json" ]; then
                echo "{}" > "$FILE_PATH"
            else
                echo "" > "$FILE_PATH"
            fi
            echo "[$(date '+%Y-%m-%d %H:%M:%S')] 清空日志文件: $FILE_PATH (原大小: ${size} 字节)"
        else
            echo "[$(date '+%Y-%m-%d %H:%M:%S')] 日志文件大小正常，无需清理"
        fi
    else
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] 日志文件不存在: $FILE_PATH"
    fi
done

echo "[$(date '+%Y-%m-%d %H:%M:%S')] 日志清理检查完成"
EOF
    
    chmod +x "$CLEAN_SCRIPT"
}

# === 启动服务 ===
start_service() {
    print_step "启动 BMS CAN 服务..."
    
    # 启用并启动服务
    "$SERVICE_FILE" enable
    "$SERVICE_FILE" start
    
    # 等待服务启动
    sleep 3
    
    # 检查服务状态
    if "$SERVICE_FILE" status >/dev/null 2>&1; then
        print_info "服务启动成功"
    else
        print_warn "服务状态检查失败，但可能正在运行"
    fi
    
    # 检查进程
    if ps | grep -q "bms_Uploader.py"; then
        print_info "BMS Uploader 进程正在运行"
    else
        print_warn "未检测到 BMS Uploader 进程"
    fi
}

# === 显示状态信息 ===
show_status() {
    print_step "显示服务状态..."
    
    echo ""
    echo "=== BMS CAN Logger 服务状态 ==="
    echo "服务文件: $SERVICE_FILE"
    echo "主程序: $MAIN_SCRIPT"
    echo "配置文件: $CONFIG_FILE"
    echo "日志文件: $OUTPUT_LOG"
    echo "错误日志: $ERROR_LOG"
    echo ""
    
    # 检查服务状态
    if [ -f "$SERVICE_FILE" ]; then
        echo "服务状态:"
        "$SERVICE_FILE" status 2>/dev/null || echo "  状态检查失败"
    fi
    
    # 检查进程
    echo ""
    echo "运行进程:"
    ps | grep -E "(bms_Uploader|python3.*bms)" | grep -v grep || echo "  未找到相关进程"
    
    # 检查 crontab
    echo ""
    echo "定时任务:"
    crontab -l 2>/dev/null | grep clean_log || echo "  未找到清理任务"
    
    echo ""
    echo "=== 常用命令 ==="
    echo "查看实时日志: tail -f $OUTPUT_LOG"
    echo "查看错误日志: tail -f $ERROR_LOG"
    echo "重启服务: $SERVICE_FILE restart"
    echo "停止服务: $SERVICE_FILE stop"
    echo "手动清理日志: $CLEAN_SCRIPT"
}

# === 主函数 ===
main() {
    echo ""
    echo "=== BMS CAN Logger 一键部署脚本 ==="
    echo "版本: 1.0"
    echo "功能: 集成 bms_uploader + bms_can_service + clean_log"
    echo ""
    
    check_environment
    setup_directories
    install_dependencies
    stop_existing_service
    create_service_file
    setup_log_cleanup
    start_service
    show_status
    
    echo ""
    print_info "=== 部署完成 ==="
    echo ""
    echo "服务已启动并设置为开机自启动"
    echo "日志清理任务已设置 (每6小时执行一次)"
    echo ""
    echo "下一步操作:"
    echo "1. 检查配置文件: $CONFIG_FILE"
    echo "2. 查看运行日志: tail -f $OUTPUT_LOG"
    echo "3. 监控服务状态: $SERVICE_FILE status"
    echo ""
}

# === 脚本入口 ===
case "$1" in
    "")
        main
        ;;
    "status")
        show_status
        ;;
    "clean")
        setup_log_cleanup
        ;;
    "restart")
        stop_existing_service
        start_service
        ;;
    *)
        echo "用法: $0 [status|clean|restart]"
        echo ""
        echo "参数说明:"
        echo "  (无参数) - 执行完整部署"
        echo "  status   - 显示服务状态"
        echo "  clean    - 重新设置日志清理"
        echo "  restart  - 重启服务"
        exit 1
        ;;
esac
